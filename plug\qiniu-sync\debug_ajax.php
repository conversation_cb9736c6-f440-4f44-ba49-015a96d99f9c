<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX 调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>七牛云同步插件 AJAX 调试</h1>
    
    <div>
        <button onclick="testGetStatus()">测试获取状态</button>
        <button onclick="testDependencies()">测试依赖检查</button>
        <button onclick="testInstall()">测试安装</button>
        <button onclick="testEnable()">测试启用</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(title, data, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function sendRequest(action, data = {}) {
            console.log('发送请求:', action, data);
            
            return fetch('install.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: action, ...data })
            })
            .then(response => {
                console.log('响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(text => {
                console.log('响应文本:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    throw new Error('服务器返回了无效的JSON响应: ' + text.substring(0, 200));
                }
            });
        }

        function testGetStatus() {
            addResult('开始测试获取状态...', {}, true);
            sendRequest('get_status')
                .then(result => {
                    addResult('获取状态成功', result, true);
                })
                .catch(error => {
                    addResult('获取状态失败', { error: error.message }, false);
                });
        }

        function testDependencies() {
            addResult('开始测试依赖检查...', {}, true);
            sendRequest('test_dependencies')
                .then(result => {
                    addResult('依赖检查完成', result, result.success);
                })
                .catch(error => {
                    addResult('依赖检查失败', { error: error.message }, false);
                });
        }

        function testInstall() {
            addResult('开始测试安装...', {}, true);
            sendRequest('install')
                .then(result => {
                    addResult('安装测试完成', result, result.success);
                })
                .catch(error => {
                    addResult('安装测试失败', { error: error.message }, false);
                });
        }

        function testEnable() {
            addResult('开始测试启用...', {}, true);
            sendRequest('enable')
                .then(result => {
                    addResult('启用测试完成', result, result.success);
                })
                .catch(error => {
                    addResult('启用测试失败', { error: error.message }, false);
                });
        }

        // 页面加载时自动测试状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testGetStatus();
            }, 500);
        });
    </script>
</body>
</html>
