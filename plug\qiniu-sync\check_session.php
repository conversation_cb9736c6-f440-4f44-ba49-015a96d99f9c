<?php
session_start();

echo "<h1>会话状态检查</h1>";

echo "<h2>会话信息</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>登录状态</h2>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p style='color: green;'>✓ 已登录管理后台</p>";
} else {
    echo "<p style='color: red;'>✗ 未登录管理后台</p>";
    echo "<p>需要先登录管理后台才能访问安装页面</p>";
    echo "<p><a href='../../admin/login.php'>点击这里登录</a></p>";
}

echo "<h2>测试链接</h2>";
echo "<p><a href='install.php'>安装页面</a></p>";
echo "<p><a href='debug_ajax.php'>AJAX调试页面</a></p>";
echo "<p><a href='test_install.php'>安装测试页面</a></p>";
?>
